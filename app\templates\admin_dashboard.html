<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Hotel Bot - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 250px;
            z-index: 1000;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 12px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(5px);
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .stat-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-4">
            <h4 class="mb-4">
                <i class="fas fa-hotel me-2"></i>
                Hotel Bot Admin
            </h4>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-section="dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#hotels" data-section="hotels">
                        <i class="fas fa-building me-2"></i>
                        Hotels
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#users" data-section="users">
                        <i class="fas fa-users me-2"></i>
                        Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#analytics" data-section="analytics">
                        <i class="fas fa-chart-bar me-2"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#monitoring" data-section="monitoring">
                        <i class="fas fa-heartbeat me-2"></i>
                        Monitoring
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#security" data-section="security">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white mb-4 rounded shadow-sm">
            <div class="container-fluid">
                <span class="navbar-brand">WhatsApp Hotel Bot Dashboard</span>
                <div class="d-flex align-items-center">
                    <span class="me-3">
                        <i class="fas fa-circle text-success me-1"></i>
                        System Online
                    </span>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </nav>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <h3 id="total-hotels" class="mb-1">-</h3>
                            <p class="mb-0">Total Hotels</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-success">
                        <div class="card-body text-center">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <h3 id="total-messages" class="mb-1">-</h3>
                            <p class="mb-0">Messages Today</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h3 id="active-guests" class="mb-1">-</h3>
                            <p class="mb-0">Active Guests</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card-info">
                        <div class="card-body text-center">
                            <i class="fas fa-robot fa-2x mb-2"></i>
                            <h3 id="ai-responses" class="mb-1">-</h3>
                            <p class="mb-0">AI Responses</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Message Volume (Last 7 Days)</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="messageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Hotel Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="hotelChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Messages</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-messages" class="loading">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Status</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-status">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Database</span>
                                    <span class="status-badge status-active">Active</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>WhatsApp API</span>
                                    <span class="status-badge status-active">Connected</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>AI Service</span>
                                    <span class="status-badge status-active">Online</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Cache</span>
                                    <span class="status-badge status-active">Running</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other sections will be loaded dynamically -->
        <div id="hotels-section" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Hotel Management</h5>
                </div>
                <div class="card-body">
                    <p>Hotel management interface will be loaded here.</p>
                </div>
            </div>
        </div>

        <div id="users-section" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">User Management</h5>
                </div>
                <div class="card-body">
                    <p>User management interface will be loaded here.</p>
                </div>
            </div>
        </div>

        <div id="analytics-section" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Analytics</h5>
                </div>
                <div class="card-body">
                    <p>Analytics interface will be loaded here.</p>
                </div>
            </div>
        </div>

        <div id="monitoring-section" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">System Monitoring</h5>
                </div>
                <div class="card-body">
                    <p>Monitoring interface will be loaded here.</p>
                </div>
            </div>
        </div>

        <div id="security-section" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Security Settings</h5>
                </div>
                <div class="card-body">
                    <p>Security interface will be loaded here.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navigation handling
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active nav item
                document.querySelectorAll('.sidebar .nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Show corresponding section
                const section = this.getAttribute('data-section');
                document.querySelectorAll('.content-section').forEach(s => s.style.display = 'none');
                document.getElementById(section + '-section').style.display = 'block';
            });
        });

        // Initialize charts
        let messageChart, hotelChart;

        function initCharts() {
            // Message Volume Chart
            const messageCtx = document.getElementById('messageChart').getContext('2d');
            messageChart = new Chart(messageCtx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Messages',
                        data: [12, 19, 3, 5, 2, 3, 9],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Hotel Activity Chart
            const hotelCtx = document.getElementById('hotelChart').getContext('2d');
            hotelChart = new Chart(hotelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Inactive', 'Pending'],
                    datasets: [{
                        data: [12, 3, 2],
                        backgroundColor: ['#38ef7d', '#f5576c', '#f093fb']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/v1/admin/dashboard');
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Update stats (placeholder data for now)
                    document.getElementById('total-hotels').textContent = '5';
                    document.getElementById('total-messages').textContent = '1,234';
                    document.getElementById('active-guests').textContent = '89';
                    document.getElementById('ai-responses').textContent = '456';
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Refresh data
        function refreshData() {
            loadDashboardData();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadDashboardData();
        });
    </script>
</body>
</html>
